@import 'tailwindcss';
@import 'tw-animate-css';
@plugin '@tailwindcss/typography';

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.7057 0.2236 264.07);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.968 0.007 247.896);
  --accent-foreground: oklch(0.208 0.042 265.755);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);

  /* Light mode primary variants */
  --primary-50: #eff4ff;
  --primary-100: #dbe6fe;
  --primary-200: #dbe6fe;
  --primary-300: #93bafd;
  --primary-400: #8fb5ff;
  --primary-500: #5c92fa;
  --primary-600: #3b6ef6;
  --primary-700: #254eeb;
  --primary-800: #1e31af;
  --primary-900: #1e2f8a;
  --primary-950: #171f54;

  --sidebar: var(--background);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--accent);
  --sidebar-accent-foreground: var(--accent-foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);
  --previa-gradient: linear-gradient(138deg, #3b81f6 0%, #4f46e5 110.52%);
  --logo-gradient: linear-gradient(142deg, #5c92fa 35.25%, #a75ffd 107.8%);
  --card-gradient: linear-gradient(180deg, #e3f1fb 0%, rgba(227, 241, 251, 0) 100%);
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --card: oklch(0.14 0.04 259.21);
  --card-foreground: oklch(0.984 0.003 247.858);
  --popover: oklch(0.208 0.042 265.755);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --primary: oklch(0.8597 0.1112 252.82);
  --primary-foreground: oklch(0.208 0.042 265.755);
  --secondary: oklch(0.279 0.041 260.031);
  --secondary-foreground: oklch(0.984 0.003 247.858);
  --muted: oklch(0.279 0.041 260.031);
  --muted-foreground: oklch(0.704 0.04 256.788);
  --accent: oklch(0.279 0.041 260.031);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);

  /* Dark mode primary variants - inverted/adjusted for dark theme */
  --primary-50: #0f1629;
  --primary-100: #1a2332;
  --primary-200: #1e2f8a;
  --primary-300: #1e31af;
  --primary-400: #254eeb;
  --primary-500: #3b6ef6;
  --primary-600: #5c92fa;
  --primary-700: #8fb5ff;
  --primary-800: #93bafd;
  --primary-900: #dbe6fe;
  --primary-950: #eff4ff;

  --strock: #e5e5e5;

  --previa-gradient: linear-gradient(138deg, #3b81f6 0%, #4f46e5 110.52%);
  --logo-gradient: linear-gradient(142deg, #5c92fa 35.25%, #a75ffd 107.8%);
  --card-gradient: linear-gradient(180deg, #e3f1fb 0%, rgba(227, 241, 251, 0) 100%);
}

@theme inline {
  --font-inter: 'Inter', 'sans-serif';
  --font-manrope: 'Manrope', 'sans-serif';
  --font-md: 14px;
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  /* Primary color variants - will automatically switch based on theme */
  --color-primary-50: var(--primary-50);
  --color-primary-100: var(--primary-100);
  --color-primary-200: var(--primary-200);
  --color-primary-300: var(--primary-300);
  --color-primary-400: var(--primary-400);
  --color-primary-500: var(--primary-500);
  --color-primary-600: var(--primary-600);
  --color-primary-700: var(--primary-700);
  --color-primary-800: var(--primary-800);
  --color-primary-900: var(--primary-900);
  --color-primary-950: var(--primary-950);

  --color-strock: #e5e5e5;
  --color-black: #010101;
  --color-gray-dark: #464646;
  --color-gray-light: #8c8c8c;

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: #5c92fa;
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
    scrollbar-width: thin;
    scrollbar-color: var(--border) transparent;
  }
  html {
    @apply overflow-x-hidden;
  }
  body {
    @apply bg-background text-foreground min-h-svh w-full;
  }

  button:not(:disabled),
  [role='button']:not(:disabled) {
    cursor: pointer;
  }

  .previa-gradient-text {
    background: var(--previa-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  /* Prevent focus zoom on mobile devices */
  @media screen and (max-width: 767px) {
    input,
    select,
    textarea {
      font-size: 16px !important;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
}

@utility no-scrollbar {
  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type='number'] {
  -moz-appearance: textfield;
}

@utility faded-bottom {
  @apply after:pointer-events-none after:absolute after:bottom-0 after:left-0 after:hidden after:h-32 after:w-full after:bg-[linear-gradient(180deg,_transparent_10%,_white_70%)] md:after:block dark:after:bg-[linear-gradient(180deg,_transparent_10%,_black_70%)];
}

/* === Collapsible Animations === */
.CollapsibleContent {
  overflow: hidden;
}
.CollapsibleContent[data-state='open'] {
  animation: slideDown 300ms ease-out;
}
.CollapsibleContent[data-state='closed'] {
  animation: slideUp 300ms ease-out;
}

@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-collapsible-content-height);
  }
  to {
    height: 0;
  }
}

@layer utilities {
  /* === Custom Animations === */
  @keyframes border-sweep {
    0% {
      transform: translateX(-100%) rotate(0deg);
      opacity: 0;
    }
    10% {
      opacity: 1;
    }
    90% {
      opacity: 1;
    }
    100% {
      transform: translateX(100%) rotate(0deg);
      opacity: 0;
    }
  }
  @keyframes border-glow {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  @keyframes shine {
    0% {
      transform: translateX(100%) rotate(6deg);
    }
    100% {
      transform: translateX(-100%) rotate(6deg);
    }
  }
  .animate-border-sweep {
    animation: border-sweep 2s ease-in-out infinite;
  }
  .animate-border-glow {
    animation: border-glow 3s ease infinite;
    background-size: 200% 200%;
  }
  .animate-shine {
    animation: shine 1.5s ease-in-out;
  }

  /* === Gradient Utilities === */
  .bg-previa-gradient {
    background: var(--previa-gradient);
  }
  .bg-logo-gradient {
    background: var(--logo-gradient);
  }
  .bg-card-gradient {
    background: var(--card-gradient);
  }
}

/* React Quill Custom Styling */

@layer components {
  /* Headings */
  .ql-editor h1 {
    @apply !mb-2 text-2xl font-semibold text-black;
  }
  .ql-editor h2 {
    @apply !mb-2 text-xl font-medium text-black;
  }
  .ql-editor h3 {
    @apply !mb-2 text-lg font-medium text-black;
  }
  .ql-editor h4 {
    @apply !mb-2 text-base font-normal text-black;
  }
  .ql-editor h5 {
    @apply !mb-1.5 text-sm font-normal text-black;
  }
  .ql-editor h6 {
    @apply !mb-1 text-xs font-light text-black;
  }

  /* Paragraphs */
  .ql-editor p:not(:last-child) {
    @apply !mt-2 !mb-4 !leading-relaxed;
  }

  /* Lists & list items */
  .ql-editor ul,
  .ql-editor ol {
    @apply !list-disc [&>li]:mb-2 [&>li]:list-inside [&>li]:!list-disc;
  }

  .ql-editor li:last-child {
    @apply !mb-4;
  }
}
