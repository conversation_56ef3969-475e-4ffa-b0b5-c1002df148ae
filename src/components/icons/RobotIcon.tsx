import { SidebarIconProps } from '@/types/sidebar';
import React from 'react';

export const RobotIcon: React.FC<SidebarIconProps> = ({ variant = 'default', ...props }) => {
  const isActive = variant === 'active';

  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      {...props}
    >
      <g clipPath='url(#clip0_726_6004)'>
        <path
          opacity={isActive ? '0.5' : '1'}
          d='M18.75 5.25H5.25C4.65326 5.25 4.08097 5.48705 3.65901 5.90901C3.23705 6.33097 3 6.90326 3 7.5V18C3 18.5967 3.23705 19.169 3.65901 19.591C4.08097 20.0129 4.65326 20.25 5.25 20.25H18.75C19.3467 20.25 19.919 20.0129 20.341 19.591C20.7629 19.169 21 18.5967 21 18V7.5C21 6.90326 20.7629 6.33097 20.341 5.90901C19.919 5.48705 19.3467 5.25 18.75 5.25ZM15.375 17.25H8.625C8.12772 17.25 7.65081 17.0525 7.29917 16.7008C6.94754 16.3492 6.75 15.8723 6.75 15.375C6.75 14.8777 6.94754 14.4008 7.29917 14.0492C7.65081 13.6975 8.12772 13.5 8.625 13.5H15.375C15.8723 13.5 16.3492 13.6975 16.7008 14.0492C17.0525 14.4008 17.25 14.8777 17.25 15.375C17.25 15.8723 17.0525 16.3492 16.7008 16.7008C16.3492 17.0525 15.8723 17.25 15.375 17.25Z'
          fill={isActive ? '#5C92FA' : 'none'}
        />
        <path
          d='M18.75 5.25H5.25C4.00736 5.25 3 6.25736 3 7.5V18C3 19.2426 4.00736 20.25 5.25 20.25H18.75C19.9926 20.25 21 19.2426 21 18V7.5C21 6.25736 19.9926 5.25 18.75 5.25Z'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M12 5.25V1.5'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M7.875 11.25C8.49632 11.25 9 10.7463 9 10.125C9 9.50368 8.49632 9 7.875 9C7.25368 9 6.75 9.50368 6.75 10.125C6.75 10.7463 7.25368 11.25 7.875 11.25Z'
          fill='currentColor'
        />
        <path
          d='M16.125 11.25C16.7463 11.25 17.25 10.7463 17.25 10.125C17.25 9.50368 16.7463 9 16.125 9C15.5037 9 15 9.50368 15 10.125C15 10.7463 15.5037 11.25 16.125 11.25Z'
          fill='currentColor'
        />
        <path
          d='M15.375 13.5H8.625C7.58947 13.5 6.75 14.3395 6.75 15.375C6.75 16.4105 7.58947 17.25 8.625 17.25H15.375C16.4105 17.25 17.25 16.4105 17.25 15.375C17.25 14.3395 16.4105 13.5 15.375 13.5Z'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M13.875 13.5V17.25'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M10.125 13.5V17.25'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_726_6004'>
          <rect width='24' height='24' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
