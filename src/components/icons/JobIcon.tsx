import { SidebarIconProps } from '@/types/sidebar';
import React from 'react';

export const JobIcon: React.FC<SidebarIconProps> = ({ variant = 'default', ...props }) => {
  const isActive = variant === 'active';

  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      {...props}
    >
      <g clipPath='url(#clip0_726_5983)'>
        <path
          opacity={isActive ? '0.5' : '1'}
          d='M8.78627 2.25C8.60571 2.25 8.4312 2.31513 8.29481 2.43345C8.15841 2.55176 8.06927 2.71531 8.04377 2.89406L6.75752 11.8941C6.74234 12.0004 6.7502 12.1088 6.78055 12.2119C6.81091 12.315 6.86305 12.4103 6.93346 12.4915C7.00386 12.5726 7.09089 12.6377 7.18864 12.6823C7.28638 12.7269 7.39257 12.75 7.50002 12.75H16.5C16.6075 12.75 16.7137 12.7269 16.8114 12.6823C16.9092 12.6377 16.9962 12.5726 17.0666 12.4915C17.137 12.4103 17.1891 12.315 17.2195 12.2119C17.2498 12.1088 17.2577 12.0004 17.2425 11.8941L15.9563 2.89406C15.9308 2.71531 15.8416 2.55176 15.7052 2.43345C15.5688 2.31513 15.3943 2.25 15.2138 2.25H8.78627Z'
          fill={isActive ? '#5C92FA' : 'none'}
        />
        <path
          d='M6.75 21.75C6.75 21.1533 6.98705 20.581 7.40901 20.159C7.83097 19.7371 8.40326 19.5 9 19.5H15C15.5967 19.5 16.169 19.7371 16.591 20.159C17.0129 20.581 17.25 21.1533 17.25 21.75'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M12 15.75V21.75'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M1.5 12H3.75C3.75 12.9946 4.14509 13.9484 4.84835 14.6517C5.55161 15.3549 6.50544 15.75 7.5 15.75H16.5C17.4946 15.75 18.4484 15.3549 19.1517 14.6517C19.8549 13.9484 20.25 12.9946 20.25 12H22.5'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M8.78627 2.25C8.60571 2.25 8.4312 2.31513 8.29481 2.43345C8.15841 2.55176 8.06927 2.71531 8.04377 2.89406L6.75752 11.8941C6.74234 12.0004 6.7502 12.1088 6.78055 12.2119C6.81091 12.315 6.86305 12.4103 6.93346 12.4915C7.00386 12.5726 7.09089 12.6377 7.18864 12.6823C7.28638 12.7269 7.39257 12.75 7.50002 12.75H16.5C16.6075 12.75 16.7137 12.7269 16.8114 12.6823C16.9092 12.6377 16.9962 12.5726 17.0666 12.4915C17.137 12.4103 17.1891 12.315 17.2195 12.2119C17.2498 12.1088 17.2577 12.0004 17.2425 11.8941L15.9563 2.89406C15.9308 2.71531 15.8416 2.55176 15.7052 2.43345C15.5688 2.31513 15.3943 2.25 15.2138 2.25H8.78627Z'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_726_5983'>
          <rect width='24' height='24' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
