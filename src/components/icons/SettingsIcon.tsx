import { SidebarIconProps } from '@/types/sidebar';
import React from 'react';

export const SettingsIcon: React.FC<SidebarIconProps> = ({ variant = 'default', ...props }) => {
  const isActive = variant === 'active';

  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      {...props}
    >
      <g clipPath='url(#clip0_726_6015)'>
        <path
          opacity={isActive ? '0.5' : '1'}
          d='M21.5722 10.1962L18.5862 8.49563C18.5262 8.38688 18.4634 8.28094 18.3987 8.17594L18.3875 4.80094C17.4438 4.00454 16.3582 3.39352 15.1878 3L12.1878 4.67719C12.0622 4.67719 11.9356 4.67719 11.8128 4.67719L8.81279 3C7.64271 3.39481 6.55775 4.00711 5.61498 4.80469L5.59998 8.17969C5.53435 8.28469 5.47154 8.39156 5.41248 8.49938L2.42748 10.1962C2.19214 11.3866 2.19214 12.6115 2.42748 13.8019L5.41342 15.5025C5.47342 15.6112 5.53623 15.7172 5.60092 15.8222L5.61217 19.1972C6.55597 19.9944 7.64189 20.606 8.81279 21L11.8128 19.3247C11.9384 19.3247 12.065 19.3247 12.1878 19.3247L15.1878 21C16.3569 20.6049 17.4409 19.9926 18.3828 19.1953L18.3978 15.8203C18.4634 15.7153 18.5262 15.6084 18.5853 15.5006L21.5703 13.8038C21.8065 12.6129 21.8071 11.3873 21.5722 10.1962ZM12.0003 15.75C11.2586 15.75 10.5336 15.5301 9.9169 15.118C9.30022 14.706 8.81957 14.1203 8.53574 13.4351C8.25191 12.7498 8.17765 11.9958 8.32235 11.2684C8.46704 10.541 8.82419 9.8728 9.34864 9.34835C9.87309 8.8239 10.5413 8.46675 11.2687 8.32205C11.9961 8.17736 12.7501 8.25162 13.4354 8.53545C14.1206 8.81928 14.7062 9.29993 15.1183 9.91661C15.5304 10.5333 15.7503 11.2583 15.7503 12C15.7503 12.9946 15.3552 13.9484 14.6519 14.6517C13.9487 15.3549 12.9949 15.75 12.0003 15.75Z'
          fill={isActive ? '#5C92FA' : 'none'}
        />
        <path
          d='M12 15.75C14.0711 15.75 15.75 14.0711 15.75 12C15.75 9.92893 14.0711 8.25 12 8.25C9.92893 8.25 8.25 9.92893 8.25 12C8.25 14.0711 9.92893 15.75 12 15.75Z'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M12.1925 19.3228C12.0669 19.3228 11.9403 19.3228 11.8175 19.3228L8.81283 21C7.64313 20.6065 6.55821 19.9959 5.61502 19.2L5.60377 15.825C5.53721 15.72 5.47439 15.6141 5.41627 15.5053L2.42846 13.8038C2.19312 12.6134 2.19312 11.3885 2.42846 10.1981L5.41346 8.50125C5.47439 8.39344 5.53721 8.28656 5.60096 8.18156L5.61596 4.80656C6.5583 4.00842 7.64294 3.39548 8.81283 3L11.8128 4.67719C11.9385 4.67719 12.065 4.67719 12.1878 4.67719L15.1878 3C16.3575 3.39346 17.4425 4.00414 18.3856 4.8L18.3969 8.175C18.4635 8.28 18.5263 8.38594 18.5844 8.49469L21.5703 10.1953C21.8057 11.3857 21.8057 12.6106 21.5703 13.8009L18.5853 15.4978C18.5244 15.6056 18.4616 15.7125 18.3978 15.8175L18.3828 19.1925C17.4411 19.9908 16.3571 20.604 15.1878 21L12.1925 19.3228Z'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_726_6015'>
          <rect width='24' height='24' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
