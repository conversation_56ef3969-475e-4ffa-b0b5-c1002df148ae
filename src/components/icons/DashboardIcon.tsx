import { SidebarIconProps } from '@/types/sidebar';
import React from 'react';

export const DashboardIcon: React.FC<SidebarIconProps> = ({ variant = 'default', ...props }) => {
  const isActive = variant === 'active';

  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      {...props}
    >
      <g clipPath='url(#clip0_726_5929)'>
        <path
          opacity={isActive ? '0.5' : '1'}
          d='M2.80253 17.4928C2.42767 16.4269 2.24083 15.304 2.25035 14.1741C2.2916 8.81252 6.72503 4.45221 12.0941 4.50002C13.6453 4.51627 15.1702 4.90208 16.5425 5.62547C17.9147 6.34885 19.0948 7.38896 19.9847 8.65955C20.8747 9.93013 21.4489 11.3946 21.6597 12.9314C21.8706 14.4683 21.7121 16.0333 21.1972 17.4966C21.146 17.6439 21.05 17.7715 20.9228 17.8617C20.7956 17.952 20.6435 18.0003 20.4875 18H3.51222C3.35598 17.9993 3.2038 17.9502 3.07668 17.8593C2.94956 17.7685 2.85377 17.6404 2.80253 17.4928Z'
          fill={isActive ? '#5C92FA' : 'none'}
        />
        <path
          d='M11.25 15L20.25 6'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M5.29125 15C5.26378 14.751 5.25001 14.5006 5.25 14.25C5.25091 13.1788 5.50655 12.1231 5.99583 11.1702C6.48511 10.2172 7.19399 9.39424 8.06399 8.76922C8.93398 8.1442 9.94014 7.73503 10.9994 7.57548C12.0587 7.41592 13.1407 7.51056 14.1562 7.85159'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M20.6835 9.81002C21.2849 10.99 21.6405 12.2797 21.7288 13.6011C21.8171 14.9225 21.6362 16.2481 21.1972 17.4975C21.146 17.6448 21.0501 17.7724 20.9229 17.8627C20.7957 17.9529 20.6435 18.0012 20.4875 18.001H3.51223C3.35599 18.0003 3.20381 17.9511 3.07669 17.8603C2.94957 17.7694 2.85378 17.6414 2.80254 17.4938C2.42757 16.4276 2.24073 15.3043 2.25035 14.1741C2.2916 8.81252 6.72504 4.45221 12.0941 4.50002C13.6074 4.51217 15.0969 4.87749 16.4441 5.5669'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_726_5929'>
          <rect width='24' height='24' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
