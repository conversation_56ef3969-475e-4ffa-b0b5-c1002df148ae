import { CrossIcon, LogoWithText } from '@/assets';
import { timerService } from '@/features/interview/candidate-interview/services/timerService';
import { ReactNode } from 'react';

interface InterviewLayoutProps {
  children: ReactNode;
  jobTitle?: string;
}

const InterviewLayout = ({ children, jobTitle }: InterviewLayoutProps) => {
  const handleExitInterview = () => {
    timerService.completeInterview('manual');
  };

  return (
    <>
      <div className='flex items-center justify-between bg-white px-6 py-5'>
        <div className='flex items-center'>
          <LogoWithText height='32' width='95' />
        </div>
        {jobTitle && (
          <h1 className='text-xl font-semibold text-gray-900'>Interview for {jobTitle}</h1>
        )}
        <div>
          <button 
            onClick={handleExitInterview}
            className='flex items-center justify-center cursor-pointer hover:bg-gray-100 p-2 rounded'
          >
            <CrossIcon />
            <span className='text-sm text-neutral-600'>Exit Interview</span>
          </button>
        </div>
      </div>
      {children}
    </>
  );
};
export default InterviewLayout;
