import {
  CandidateVerifyRequest,
  completeInterview,
  generateQuestion,
  generateQuestionSync,
  GenerateQuestionSyncRequest,
  prepareInterview,
  PrepareInterviewRequest,
  QuestionRequest,
  runEvaluation,
  updateAnswer,
  UpdateAnswerRequest,
  verifyCandidate,
} from '@/services/interview';
import { generateQuestionAPI, GenerateQuestionRequest } from '@/services/question';
import { useMutation, useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { toast } from 'sonner';

export const interviewKeys = {
  all: ['interview'] as const,
  sessions: () => [...interviewKeys.all, 'session'] as const,
  session: (id: string) => [...interviewKeys.sessions(), id] as const,
  questions: () => [...interviewKeys.all, 'question'] as const,
  question: (sessionId: string, questionIndex: number) =>
    [...interviewKeys.questions(), sessionId, questionIndex] as const,
};

export function useVerifyCandidateMutation() {
  return useMutation({
    mutationKey: ['verify-candidate'],
    mutationFn: async (payload: CandidateVerifyRequest) => {
      const response = await verifyCandidate(payload);
      return response.data;
    },
    onSuccess: (data) => {
      console.log(data);
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Candidate verification failed';
        toast.error(errorMessage);
      }
    },
  });
}

// Generate Question Mutation
export function useGenerateQuestionMutation() {
  return useMutation({
    mutationKey: ['generate-question'],
    mutationFn: async (payload: QuestionRequest) => {
      const response = await generateQuestion(payload);
      return response.data;
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to generate question';
        toast.error(errorMessage);
        console.error('Question generation failed:', error);
      }
    },
  });
}

// Prepare Interview Mutation
export function usePrepareInterviewMutation() {
  return useMutation({
    mutationKey: ['prepare-interview'],
    mutationFn: async (payload: PrepareInterviewRequest) => {
      const response = await prepareInterview(payload);
      return response.data;
    },
    onSuccess: (data) => {
      console.log('Interview prepared successfully:', data);
      toast.success('Interview prepared successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to prepare interview';
        toast.error(errorMessage);
        console.error('Interview preparation failed:', error);
      }
    },
  });
}

// Generate Question via API
export function useGenerateQuestionAPIMutation() {
  return useMutation({
    mutationKey: ['generate-question-api'],
    mutationFn: async (payload: GenerateQuestionRequest) => {
      const response = await generateQuestionAPI(payload);
      return response.data;
    },
    onSuccess: (data) => {
      console.log('Question generation request sent successfully:', data);
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage =
          error.response?.data?.message || 'Failed to request question generation';
        toast.error(errorMessage);
        console.error('Question generation request failed:', error);
      }
    },
  });
}

// Run Evaluation Query
export function useRunEvaluationQuery(interviewId: string, options = { enabled: true }) {
  return useQuery({
    queryKey: ['run-evaluation', interviewId],
    queryFn: async () => {
      const response = await runEvaluation(interviewId);
      return response.data;
    },
    enabled: options.enabled && !!interviewId,
    staleTime: 5 * 60 * 1000,
    retry: 3,
  });
}

// Generate Question Sync Mutation (REST API replacement for WebSocket)
export function useGenerateQuestionSyncMutation() {
  return useMutation({
    mutationKey: ['generate-question-sync'],
    mutationFn: async (payload: GenerateQuestionSyncRequest) => {
      const response = await generateQuestionSync(payload);
      return response.data;
    },
    onSuccess: (data) => {
      console.log('Question generated successfully:', data);
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to generate question';
        toast.error(errorMessage);
        console.error('Question generation failed:', error);
      }
    },
  });
}

// Complete Interview Mutation
export function useCompleteInterviewMutation() {
  return useMutation({
    mutationKey: ['complete-interview'],
    mutationFn: async (interviewId: string) => {
      const response = await completeInterview(interviewId);
      return response.data;
    },
    onSuccess: (data) => {
      console.log('Interview completed successfully:', data);
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to complete interview';
        toast.error(errorMessage);
        console.error('Interview completion failed:', error);
      }
    },
  });
}

// Update Answer Mutation
export function useUpdateAnswerMutation() {
  return useMutation({
    mutationKey: ['update-answer'],
    mutationFn: async (payload: UpdateAnswerRequest) => {
      const response = await updateAnswer(payload);
      return response.data;
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to update answer';
        toast.error(errorMessage);
        console.error('Answer update failed:', error);
      }
    },
  });
}

export { useQuestionWebSocket } from '@/features/interview/candidate-interview/services/questionWebSocketService';
