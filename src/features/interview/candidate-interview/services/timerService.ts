import { 
  overallTimeRemaining, 
  thinkingTimeRemaining, 
  answerTimeRemaining, 
  editTimeRemaining,
  currentPhase,
  InterviewPhase,
} from '../signals/timerSignals';
import { interviewProgress } from '../signals/interviewSignals';
import SessionStorageManager, { SESSION_STORAGE_KEYS, InterviewSessionStorage } from '@/utils/sessionStorage';

class TimerService {
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private autoSubmitCallbacks: Map<string, () => void> = new Map();
  private completeInterviewCallback: ((interviewId: string) => Promise<void>) | null = null;

  // Register callback for interview completion API call
  registerCompleteInterviewCallback(callback: (interviewId: string) => Promise<void>) {
    this.completeInterviewCallback = callback;
  }

  startOverallTimer(duration: number) {
    this.stopTimer('overall');
    
    overallTimeRemaining.value = duration;
    
    const interval = setInterval(() => {
      if (overallTimeRemaining.value > 0) {
        overallTimeRemaining.value--;
      } else {
        this.handleTimeUp();
      }
    }, 1000);
    
    this.intervals.set('overall', interval);
  }

  startThinkingTimer(duration: number = 30) {
    this.stopTimer('thinking');
    
    thinkingTimeRemaining.value = duration;
    currentPhase.value = InterviewPhase.THINKING;
    
    const interval = setInterval(() => {
      if (thinkingTimeRemaining.value > 0) {
        thinkingTimeRemaining.value--;        
      } else {
        this.stopTimer('thinking');
        // Trigger any registered callback for thinking timeout
        const callback = this.autoSubmitCallbacks.get('thinking');
        if (callback) {
          callback();
        } else {
          // Auto-transition to answering if no callback
          this.startAnswerTimer();
        }
      }
    }, 1000);
    
    this.intervals.set('thinking', interval);
  }

  startAnswerTimer(duration: number = 60) {
    this.stopTimer('answer');
    
    answerTimeRemaining.value = duration;
    currentPhase.value = InterviewPhase.ANSWERING;
    
    const interval = setInterval(() => {
      if (answerTimeRemaining.value > 0) {
        answerTimeRemaining.value--;
      } else {
        this.stopTimer('answer');
        // Auto-transition to transcribing
        currentPhase.value = InterviewPhase.TRANSCRIBING;
        // Trigger any registered callback for answer timeout
        const callback = this.autoSubmitCallbacks.get('answer');
        if (callback) {
          callback();
        }
      }
    }, 1000);
    
    this.intervals.set('answer', interval);
  }

  startEditTimer() {
    this.stopTimer('edit');
    
    editTimeRemaining.value = 30;
    currentPhase.value = InterviewPhase.EDITING;
    
    const interval = setInterval(() => {
      if (editTimeRemaining.value > 0) {
        editTimeRemaining.value--;
      } else {
        this.stopTimer('edit');
        // Trigger any registered callback for edit timeout
        const callback = this.autoSubmitCallbacks.get('edit');
        if (callback) {
          callback();
        }
      }
    }, 1000);
    
    this.intervals.set('edit', interval);
  }

  // Register callbacks for auto-submit
  registerAutoSubmitCallback(phase: 'thinking' | 'answer' | 'edit', callback: () => void) {
    this.autoSubmitCallbacks.set(phase, callback);
  }

  // Unregister callbacks
  unregisterAutoSubmitCallback(phase: 'thinking' | 'answer' | 'edit') {
    this.autoSubmitCallbacks.delete(phase);
  }

  private handleTimeUp() {
    if (currentPhase.value === InterviewPhase.ANSWERING) {
      interviewProgress.value = {
        ...interviewProgress.value,
        completionReason: 'time_up'
      };
    } else {
      this.completeInterview('time_up');
    }
  }

  async completeInterview(reason: 'time_up' | 'all_questions' | 'manual') {
    this.cleanup();
    
    // Call API if callback is registered
    if (this.completeInterviewCallback) {
      const channelId = InterviewSessionStorage.getChannelId();
      if (channelId) {
        console.log('Completing interview via API:', channelId);
        try {
          await this.completeInterviewCallback(channelId);
        } catch (error) {
          console.error('Failed to complete interview via API:', error);
        }
      }
    }

    // Clear session storage
    this.clearSessionStorage();

    interviewProgress.value = {
      ...interviewProgress.value,
      isCompleted: true,
      completionReason: reason
    };
    currentPhase.value = InterviewPhase.COMPLETED;
  }

  private clearSessionStorage() {
    SessionStorageManager.removeItem(SESSION_STORAGE_KEYS.CANDIDATE_DATA);
    SessionStorageManager.removeItem(SESSION_STORAGE_KEYS.INTERVIEW_ITEMS);
    SessionStorageManager.removeItem(SESSION_STORAGE_KEYS.CHANNEL_ID);
    SessionStorageManager.removeItem(SESSION_STORAGE_KEYS.CHAT_ID);
  }

  stopTimer(type: string) {
    const interval = this.intervals.get(type);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(type);
    }
  }

  cleanup() {
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals.clear();
    this.autoSubmitCallbacks.clear();
  }

  forceTransitionToAnswering() {
    this.stopTimer('thinking');
    this.startAnswerTimer();
  }

  forceTransitionToTranscribing() {
    this.stopTimer('answer');
    currentPhase.value = InterviewPhase.TRANSCRIBING;
  }
}

export const timerService = new TimerService();





