import { STTServiceInstance } from '@/services';
import { API_ROUTES } from '@/utils/constants';

interface TranscriptionResponse {
  text: string;
}

class STTService {
  async transcribeAudio(audioBlob: Blob, channelId?: string | null, chatId?: string | null): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('file', audioBlob, 'recording.webm');
      
      if (channelId) {
        formData.append('channel_id', channelId);
      }
      
      if (chatId) {
        formData.append('chat_id', chatId);
      }

      const response = await STTServiceInstance.callPostApi<TranscriptionResponse, FormData>(
        API_ROUTES.STT.TRANSCRIBE_SYNC,
        formData,
        null, // no authorization
        'multipart/form-data',
      );

      return response.data.text || '';
    } catch (error) {
      console.error('STT transcription failed:', error);
      throw new Error('Failed to transcribe audio');
    }
  }
}

export const sttService = new STTService();
