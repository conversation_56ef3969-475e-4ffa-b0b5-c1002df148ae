import { mediaService } from '../services/mediaService';
import { sttService } from '../services/sttService';
import { timerService } from '../services/timerService';
import { answerState } from '../signals/interviewSignals';
import { answerTimeRemaining, currentPhase, InterviewPhase } from '../signals/timerSignals';
import SoundWaveAnimation from '@/assets/animations/SoundWaveAnimation';
import { getFormatTime } from '@/utils/helper';
import { InterviewSessionStorage } from '@/utils/sessionStorage';
import { useSignals } from '@preact/signals-react/runtime';
import { Check, ClockFading } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

export function AnswerRecording() {
  useSignals();
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [_transcribedText, setTranscribedText] = useState('');
  const [_isTranscribing, setIsTranscribing] = useState(false);
  const chunksRef = useRef<Blob[]>([]);

  // Auto-start recording when entering ANSWERING phase
  useEffect(() => {
    if (currentPhase.value === InterviewPhase.ANSWERING && !isRecording) {
      console.log('Auto-starting recording for answering phase');
      startRecording();
    }
  }, [currentPhase.value]);

  // Register auto-submit callback
  useEffect(() => {
    const handleAutoSubmit = () => {
      if (isRecording) {
        console.log('Auto-submit triggered, stopping recording');
        stopRecording();
      }
    };

    timerService.registerAutoSubmitCallback('answer', handleAutoSubmit);

    return () => {
      timerService.unregisterAutoSubmitCallback('answer');
    };
  }, [isRecording]);

  const startRecording = async () => {
    if (chunksRef.current) stopRecording();
    try {
      const recorder = await mediaService.startRecording();
      if (!recorder) {
        console.error('Failed to get media recorder');
        return;
      }

      chunksRef.current = [];
      setTranscribedText('');

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      recorder.onstop = async () => {
        console.log('Recording stopped, starting transcription');
        const blob = new Blob(chunksRef.current, { type: 'audio/webm' });

        // Get channel_id and chat_id from session storage
        const channelId = InterviewSessionStorage.getChannelId();
        const chatId = InterviewSessionStorage.getChatId();

        // Start transcription immediately
        setIsTranscribing(true);
        try {
          const transcription = await sttService.transcribeAudio(blob, channelId, chatId);
          console.log('Transcription result:', transcription);
          setTranscribedText(transcription);

          answerState.value = {
            ...answerState.value,
            recordingBlob: blob,
            transcribedText: transcription,
            isRecording: false,
          };
        } catch (error) {
          console.error('Transcription failed:', error);
          setTranscribedText('Transcription failed. Please try again.');

          answerState.value = {
            ...answerState.value,
            recordingBlob: blob,
            transcribedText: '',
            isRecording: false,
          };
        } finally {
          setIsTranscribing(false);
          currentPhase.value = InterviewPhase.TRANSCRIBING;
        }
      };

      recorder.start();
      setMediaRecorder(recorder);
      setIsRecording(true);

      answerState.value = {
        ...answerState.value,
        isRecording: true,
      };

      console.log('Recording started successfully');
    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && isRecording) {
      console.log('Stopping recording manually');
      mediaRecorder.stop();
      setIsRecording(false);
      timerService.stopTimer('answer');
    }
  };

  const handleManualStop = () => {
    stopRecording();
  };

  if (currentPhase.value !== InterviewPhase.ANSWERING) return null;

  return (
    <div className='space-y-4'>
      {/* Header with timer */}
      <div className='mb-0 flex items-center justify-between'>
        <h3 className='text-xl font-semibold text-gray-900'>You may speak now. We're listening…</h3>
        <div className='flex items-center gap-2 text-sm'>
          <ClockFading className='size-4 text-orange-600' />
          <span className='text-gray-400'>
            Time to Talk:
            <span className='font-medium text-gray-900'>
              {` ${getFormatTime(answerTimeRemaining.value)} min`}
            </span>
          </span>
        </div>
      </div>

      <div>
        <span className='text-xs text-gray-500'>
          Done talking? Click the button to review your answer
        </span>
      </div>

      {/* Recording status */}
      <div className='rounded-lg p-4'>
        <div className='flex h-full w-full items-center justify-center'>
          <SoundWaveAnimation />
        </div>

        <button
          onClick={handleManualStop}
          disabled={!isRecording}
          className='flex w-full items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-3 text-sm font-medium text-white transition hover:bg-blue-700 disabled:opacity-50'
        >
          <Check className='size-4' />
          Transcribe my answer
        </button>
      </div>
    </div>
  );
}
