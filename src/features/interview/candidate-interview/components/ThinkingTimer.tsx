"use client"

import { useSignals } from "@preact/signals-react/runtime"
import { useEffect } from "react"
import { thinkingTimeRemaining, currentPhase, InterviewPhase } from "../signals/timerSignals"
import { timerService } from "../services/timerService"
import { ClockFading, Mic } from 'lucide-react';

export function ThinkingTimer() {
  useSignals()

  // Register auto-submit callback for thinking phase
  useEffect(() => {
    const handleAutoSubmit = () => {
      handleReady()
    }

    timerService.registerAutoSubmitCallback("thinking", handleAutoSubmit)

    return () => {
      timerService.unregisterAutoSubmitCallback("thinking")
    }
  }, [])

  if (currentPhase.value !== InterviewPhase.THINKING) return null

  const handleReady = () => {
    timerService.forceTransitionToAnswering()
  }

  return (
    <div className="rounded-lg h-full bg-gray-50 p-8 flex flex-col items-center justify-center">
      <div className="text-center max-w-md">
        <h3 className="text-2xl font-semibold text-gray-900 mb-4">Get Ready to Answer...</h3>

        <p className="text-gray-400 text-xs mb-8 leading-relaxed">
          After the 30-second thinking time, recording will start automatically, or you can begin recording early by
          clicking the button below.
        </p>

        <div className="flex items-center justify-center gap-2 mb-8">
          <ClockFading className='size-4 text-orange-600' />
          <span className="text-gray-700">
            Time to think:{" "}
            <span className="font-semibold">
              {Math.floor(thinkingTimeRemaining.value / 60)}:
              {(thinkingTimeRemaining.value % 60).toString().padStart(2, "0")}min
            </span>
          </span>
          </div>

      </div>
        <button
          onClick={handleReady}
          className="w-full rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 py-2 text-white font-medium transition hover:from-blue-600 hover:to-purple-700 flex items-center justify-center gap-2"
        >
            <Mic className="size-7 text-white" />
          Start Answering
        </button>
    </div>
  )
}
