import { timerService } from '../services/timerService';
import { currentQuestion, interviewProgress } from '../signals/interviewSignals';
import { canRequestNewQuestion, currentPhase, InterviewPhase } from '../signals/timerSignals';
import PreviaAvatar from '@/assets/icons/previa-avatar';
import { useGenerateQuestionSyncMutation } from '@/hooks/api/use-interview';
import { InterviewItem } from '@/services/interview';
import { InterviewSessionStorage } from '@/utils/sessionStorage';
import { useSignals } from '@preact/signals-react/runtime';
import { useEffect, useRef, useState } from 'react';

export function QuestionDisplay() {
  useSignals();
  const [isLoadingQuestion, setIsLoadingQuestion] = useState(false);
  const [hasInitialQuestionRequested, setHasInitialQuestionRequested] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);
  const requestInProgress = useRef(false);

  const generateQuestionSyncMutation = useGenerateQuestionSyncMutation();

  // Request initial question when component mounts
  useEffect(() => {
    if (!hasInitialQuestionRequested) {
      requestQuestion();
      setHasInitialQuestionRequested(true);
    }
  }, [hasInitialQuestionRequested]);

  const requestQuestion = async () => {
    if (isLoadingQuestion || requestInProgress.current) return;

    requestInProgress.current = true;
    setIsLoadingQuestion(true);

    const interviewData = InterviewSessionStorage.getInterviewItems() as InterviewItem[];

    try {
      const response = await generateQuestionSyncMutation.mutateAsync({
        channel_id: interviewData?.[0]?.id,
      });

      // Store channel_id and chat_id in session storage
      InterviewSessionStorage.setChannelId(response.channel_id);
      InterviewSessionStorage.setChatId(response.chat_id);

      // Handle the response with question text and audio
      const questionId = `q_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      currentQuestion.value = {
        text: response.content,
        expectedDuration: 60,
        isLoading: false,
        questionId: questionId,
        audioData: response.audio_data,
      };

      currentPhase.value = InterviewPhase.READING_QUESTION;

      // Play audio if available
      if (response.audio_data) {
        playAudioFromBase64(response.audio_data);
      } else {
        timerService.startThinkingTimer();
      }
    } catch (error) {
      console.error('Failed to generate question:', error);
    } finally {
      setIsLoadingQuestion(false);
      requestInProgress.current = false;
    }
  };

  const playAudioFromBase64 = async (base64Audio: string) => {
    try {
      const audioBlob = base64ToBlob(base64Audio, 'audio/wav');
      const audioUrl = URL.createObjectURL(audioBlob);

      if (audioRef.current) {
        audioRef.current.src = audioUrl;
        await audioRef.current.play();

        audioRef.current.onended = () => {
          URL.revokeObjectURL(audioUrl);
          timerService.startThinkingTimer();
        };
      }
    } catch (error) {
      console.error('Failed to play TTS audio:', error);
      timerService.startThinkingTimer();
    }
  };

  const base64ToBlob = (base64: string, mimeType: string): Blob => {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  };

  // const showCompletionMessage = () => {
  //   const completionMessage = `Thank you for participating in this interview. Your responses have been recorded and our team will review them shortly.`;

  //   currentQuestion.value = {
  //     ...currentQuestion.value,
  //     text: completionMessage,
  //     isLoading: false,
  //   };
  // };

  const getDisplayContent = () => {
    if (interviewProgress.value.isCompleted || !canRequestNewQuestion.value) {
      return {
        speaker: 'Previa',
        message: currentQuestion.value.text || `Thank you for participating in this interview.`,
      };
    }

    if (
      (isLoadingQuestion || currentPhase.value === InterviewPhase.LOADING_QUESTION) &&
      !currentQuestion.value.text
    ) {
      return {
        speaker: 'Previa',
        message: 'Generating your next question...',
      };
    }

    return {
      speaker: 'Previa',
      message: currentQuestion.value.text || 'Welcome to your interview!',
    };
  };

  const content = getDisplayContent();

  // Watch for phase changes to handle audio playback
  useEffect(() => {
    if (currentPhase.value === InterviewPhase.READING_QUESTION && currentQuestion.value.audioData) {
      playAudioFromBase64(currentQuestion.value.audioData);
    } else if (currentPhase.value === InterviewPhase.READING_QUESTION && !currentQuestion.value.audioData) {
      timerService.startThinkingTimer();
    }
  }, [currentPhase.value, currentQuestion.value.audioData]);

  return (
    <div className='flex items-start gap-6'>
      <audio ref={audioRef} style={{ display: 'none' }} />
      <div className='flex-shrink-0'>
        <div className='border-primary flex size-28 flex-col items-center justify-center gap-5 rounded-2xl border bg-gray-50'>
          <PreviaAvatar className='size-10' />
          <div className='flex w-full items-center justify-around'>
            <span className='text-xs font-medium text-gray-700'>Previa</span>
            <div className='mt-1 flex animate-pulse space-x-1'>
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className='size-1 rounded-full bg-blue-400' />
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className='flex-1'>
        {/* <div className='mb-4'>
          <span className='text-lg leading-relaxed font-medium text-gray-900'>
            {content.speaker}
          </span>
        </div> */}
        <div className='rounded-lg p-4'>
          <p className='leading-relaxed font-semibold text-gray-800'>{content.message}</p>
        </div>
      </div>
    </div>
  );
}
