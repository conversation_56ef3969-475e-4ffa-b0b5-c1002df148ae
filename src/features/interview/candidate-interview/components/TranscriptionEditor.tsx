import { sttService } from '../services/sttService';
import { timerService } from '../services/timerService';
import { answerState, currentQuestion, qaHistory } from '../signals/interviewSignals';
import { currentPhase, editTimeRemaining, InterviewPhase } from '../signals/timerSignals';
import PreviaAvatar from '@/assets/icons/previa-avatar';
import { useGenerateQuestionSyncMutation, useUpdateAnswerMutation } from '@/hooks/api/use-interview';
import { InterviewItem } from '@/services/interview';
import { InterviewSessionStorage } from '@/utils/sessionStorage';
import { useSignals } from '@preact/signals-react/runtime';
import { ClockFading } from 'lucide-react';
import { useEffect, useRef } from 'react';

export function TranscriptionEditor() {
  useSignals();
  const generateQuestionSyncMutation = useGenerateQuestionSyncMutation();
  const updateAnswerMutation = useUpdateAnswerMutation();
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    if (currentPhase.value === InterviewPhase.TRANSCRIBING) {
      transcribeAnswer();
    }
  }, [currentPhase.value]);

  const transcribeAnswer = async () => {
    if (!answerState.value.recordingBlob) {
      currentPhase.value = InterviewPhase.EDITING;
      return;
    }

    answerState.value = {
      ...answerState.value,
      isTranscribing: true,
    };

    try {
      // Get channel_id and chat_id from session storage
      const channelId = InterviewSessionStorage.getChannelId();
      const chatId = InterviewSessionStorage.getChatId();

      const transcribedText = await sttService.transcribeAudio(
        answerState.value.recordingBlob as Blob,
        channelId,
        chatId
      );

      answerState.value = {
        ...answerState.value,
        transcribedText,
        editedAnswer: transcribedText,
        isTranscribing: false,
      };
      currentPhase.value = InterviewPhase.EDITING;
      timerService.startEditTimer();
    } catch (error) {
      console.error('Transcription failed:', error);
      answerState.value = {
        ...answerState.value,
        transcribedText: 'Transcription failed. Please type your answer.',
        editedAnswer: '',
        isTranscribing: false,
      };
      currentPhase.value = InterviewPhase.EDITING;
      timerService.startEditTimer();
    }
  };

  // const playAudioFromBase64 = async (base64Audio: string) => {
  //   try {
  //     const audioBlob = base64ToBlob(base64Audio, 'audio/wav');
  //     const audioUrl = URL.createObjectURL(audioBlob);

  //     if (audioRef.current) {
  //       audioRef.current.src = audioUrl;
  //       await audioRef.current.play();

  //       audioRef.current.onended = () => {
  //         URL.revokeObjectURL(audioUrl);
  //         timerService.startThinkingTimer();
  //       };
  //     }
  //   } catch (error) {
  //     console.error('Failed to play TTS audio:', error);
  //     timerService.startThinkingTimer();
  //   }
  // };

  // const base64ToBlob = (base64: string, mimeType: string): Blob => {
  //   const byteCharacters = atob(base64);
  //   const byteNumbers = new Array(byteCharacters.length);
  //   for (let i = 0; i < byteCharacters.length; i++) {
  //     byteNumbers[i] = byteCharacters.charCodeAt(i);
  //   }
  //   const byteArray = new Uint8Array(byteNumbers);
  //   return new Blob([byteArray], { type: mimeType });
  // };

  const handleSubmit = async () => {
    try {
      const interviewData = InterviewSessionStorage.getInterviewItems() as InterviewItem[];
      const channelId = InterviewSessionStorage.getChannelId();
      const chatId = InterviewSessionStorage.getChatId();

      // First, update the answer
      await updateAnswerMutation.mutateAsync({
        channel_id: channelId,
        chat_id: chatId,
        answer: answerState.value.editedAnswer,
      });

      // Then generate the next question
      const syncResponse = await generateQuestionSyncMutation.mutateAsync({
        channel_id: interviewData?.[0]?.id,
      });

      // Update current question with the new content
      const questionId = `q_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      currentQuestion.value = {
        text: syncResponse.content,
        expectedDuration: 60,
        isLoading: false,
        questionId: questionId,
        audioData: syncResponse.audio_data, // Store audio data in question
      };

      // Store updated session data
      InterviewSessionStorage.setChannelId(syncResponse.channel_id);
      InterviewSessionStorage.setChatId(syncResponse.chat_id);

      // Add to Q&A history
      qaHistory.value = [
        ...qaHistory.value,
        {
          question: currentQuestion.value.text,
          answer: answerState.value.editedAnswer,
        },
      ];

      // Set phase to reading question - QuestionDisplay will handle audio
      currentPhase.value = InterviewPhase.READING_QUESTION;

      console.log('Answer submitted successfully');
    } catch (error) {
      console.error('Failed to submit answer:', error);
    }

    // Reset answer state
    answerState.value = {
      currentAnswer: '',
      transcribedText: '',
      editedAnswer: '',
      isRecording: false,
      isTranscribing: false,
      recordingBlob: null,
    };
  };

  if (currentPhase.value === InterviewPhase.TRANSCRIBING) {
    return (
      <div className='flex h-full items-center justify-center bg-gray-50 p-8'>
        <audio ref={audioRef} style={{ display: 'none' }} />
        <div className='flex max-w-md flex-col items-center text-center'>
          <PreviaAvatar className='size-10' />

          <h3 className='mb-3 text-xl font-semibold text-gray-900'>
            Just a moment... we're analyzing your response
          </h3>

          <p className='text-sm text-gray-600'>
            After transcription, you'll have 30 sec to review your answer.
          </p>
        </div>
      </div>
    );
  }

  if (currentPhase.value === InterviewPhase.EDITING) {
    return (
      <div className='space-y-4'>
        <audio ref={audioRef} style={{ display: 'none' }} />
        {/* Header with timer */}
        <div className='flex items-center justify-between'>
          <h3 className='text-lg font-medium text-gray-900'>Your answer</h3>
          <div className='flex items-center gap-2 text-sm text-orange-600'>
            <ClockFading className='size-4' />
            <span>Time to Talk: 0:{editTimeRemaining.value.toString().padStart(2, '0')} min</span>
          </div>
        </div>

        {/* Answer textarea */}
        <textarea
          className='h-40 w-full resize-none rounded-md border border-gray-300 p-4 text-sm text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none'
          value={answerState.value.editedAnswer}
          onChange={(e) =>
            (answerState.value = {
              ...answerState.value,
              editedAnswer: e.target.value,
            })
          }
          placeholder='Type or edit your answer here...'
          onPaste={(e) => e.preventDefault()}
          onCopy={(e) => e.preventDefault()}
          onCut={(e) => e.preventDefault()}
          onDrop={(e) => e.preventDefault()}
          onDragOver={(e) => e.preventDefault()}
        />

        {/* Submit button */}
        <button
          onClick={handleSubmit}
          disabled={generateQuestionSyncMutation.isPending}
          className='flex w-full items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-3 text-sm font-medium text-white transition hover:bg-blue-700 disabled:opacity-50'
        >
          {generateQuestionSyncMutation.isPending ? (
            <>
              <div className='h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent'></div>
              Submitting...
            </>
          ) : (
            <>
              <span>✓</span>
              Submit my answer
            </>
          )}
        </button>

        {generateQuestionSyncMutation.isError && (
          <p className='text-center text-sm text-red-500'>
            Failed to submit answer, but continuing interview
          </p>
        )}
      </div>
    );
  }

  return null;
}
